/// -----
/// file_upload_remote_data_source.dart
///
/// 文件上传远程数据源，处理与文件上传相关的API调用
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dio/dio.dart';
import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/network/dio_client.dart';
import '../../models/file_requirement_model.dart';

/// 文件上传远程数据源接口
///
/// 定义与远程API交互的方法
abstract class FileUploadRemoteDataSource {
  /// 获取文件要求列表
  ///
  /// [planId] 计划ID
  /// 返回：List<FileRequirementModel> 文件要求列表
  /// 抛出：ServerException 服务器异常
  Future<List<FileRequirementModel>> getFileRequirements({
    required String planId,
  });

  /// 上传文件到文件服务器
  ///
  /// [filePath] 文件路径
  /// [fileName] 文件名
  /// [onProgress] 上传进度回调
  /// 返回：Map<String, dynamic> 包含fileName和fileUrl的响应数据
  /// 抛出：ServerException 服务器异常
  Future<Map<String, dynamic>> uploadFileToServer({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  });

  /// 保存文件信息到后端
  ///
  /// [fileCode] 文件代码
  /// [fileName] 文件名
  /// [fileType] 文件类型
  /// [fileUrl] 文件URL
  /// [planId] 计划ID
  /// 返回：int 保存结果ID
  /// 抛出：ServerException 服务器异常
  Future<int> saveFileInfo({
    required int fileCode,
    required String fileName,
    required String fileType,
    required String fileUrl,
    required int planId,
  });
}

/// 文件上传远程数据源实现
/// 
/// 实现与远程API交互的方法
class FileUploadRemoteDataSourceImpl implements FileUploadRemoteDataSource {
  FileUploadRemoteDataSourceImpl(this._dioClient);

  final DioClient _dioClient;

  @override
  Future<List<FileRequirementModel>> getFileRequirements({
    required String planId,
  }) async {
    try {
      final response = await _dioClient.get(
        'internshipservice/v1/internship/student/file/require/list',
        queryParameters: {
          'planId': planId,
        },
      );

      // DioClient的get方法已经提取了data字段，所以response直接是数据列表
      final responseData = response;

      // 检查响应数据是否为列表
      if (responseData is List) {
        final List<dynamic> dataList = responseData;

        final models = dataList
            .map((json) => FileRequirementModel.fromJson(json))
            .toList();

        return models;
      } else {
        return []; // 返回空列表而不是抛出异常
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取文件要求列表失败: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> uploadFileToServer({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  }) async {
    try {
      // 创建FormData
      final formData = FormData.fromMap({
        'type': '10', // 固定值
        'file': await MultipartFile.fromFile(
          filePath,
          filename: fileName,
        ),
      });

      final response = await _dioClient.post(
        'v1/common/file/upload',
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            final progress = sent / total;
            onProgress(progress);
          }
        },
      );

      // DioClient已经处理了响应格式，直接返回data字段
      if (response is Map<String, dynamic>) {
        return response;
      } else {
        throw ServerException('文件上传响应格式错误');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('文件上传失败: $e');
    }
  }

  @override
  Future<int> saveFileInfo({
    required int fileCode,
    required String fileName,
    required String fileType,
    required String fileUrl,
    required int planId,
  }) async {
    try {
      final response = await _dioClient.post(
        'internshipservice/v1/internship/student/file/save',
        data: {
          'fileCode': fileCode,
          'fileName': fileName,
          'fileType': fileType,
          'fileUrl': fileUrl,
          'planId': planId,
        },
      );

      // DioClient已经处理了响应格式，直接返回data字段
      if (response is int) {
        return response;
      } else {
        throw ServerException('保存文件信息响应格式错误');
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('保存文件信息失败: $e');
    }
  }
}
