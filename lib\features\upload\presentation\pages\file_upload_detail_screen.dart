/// -----
/// file_upload_detail_screen.dart
/// 
/// 文件上传详情页面，根据不同状态显示不同的UI界面
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'dart:io';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/user_avatar.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_section_card.dart';
import 'package:flutter_demo/features/upload/models/file_upload_item.dart';
import 'package:flutter_demo/features/upload/presentation/widgets/template_download_widget.dart';
import 'package:flutter_demo/features/upload/presentation/widgets/file_upload_widget.dart';
import 'package:flutter_demo/features/upload/presentation/widgets/uploaded_files_widget.dart';
import 'package:flutter_demo/features/upload/presentation/bloc/file_upload_bloc.dart';
import 'package:flutter_demo/features/upload/presentation/bloc/file_upload_event.dart';
import 'package:flutter_demo/features/upload/presentation/bloc/file_upload_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 文件上传详情页面
class FileUploadDetailScreen extends StatelessWidget {
  /// 文件上传项信息
  final FileUploadItem uploadItem;

  const FileUploadDetailScreen({
    Key? key,
    required this.uploadItem,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<FileUploadBloc>(),
      child: FileUploadDetailView(uploadItem: uploadItem),
    );
  }
}

/// 文件上传详情页面视图
class FileUploadDetailView extends StatefulWidget {
  final FileUploadItem uploadItem;

  const FileUploadDetailView({
    Key? key,
    required this.uploadItem,
  }) : super(key: key);

  @override
  State<FileUploadDetailView> createState() => _FileUploadDetailViewState();
}

class _FileUploadDetailViewState extends State<FileUploadDetailView> {
  late FileUploadItem _uploadItem;
  File? _selectedFile;

  @override
  void initState() {
    super.initState();
    _uploadItem = widget.uploadItem;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: CustomAppBar(
        title: '${_uploadItem.fileName}上传',
      ),
      body: BlocListener<FileUploadBloc, FileUploadState>(
        listener: (context, state) {
          if (state is FileUploadSuccessState) {
            AppSnackBar.showSuccess(context, state.message);
            // 上传成功后可以刷新页面或导航
          } else if (state is FileUploadFailureState) {
            AppSnackBar.showError(context, state.message);
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 学年学期选择器
              CourseHeaderSection(courseName: _uploadItem.courseInfo),

              // 根据状态显示不同内容
              if (_uploadItem.status == FileUploadStatus.notUploaded) ...[
                // 未上传状态：显示模板下载和上传区域
                _buildNotUploadedContent(),
              ] else ...[
                // 已上传状态：显示已上传文件和审批状态
                _buildUploadedContent(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建未上传状态的内容
  Widget _buildNotUploadedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 模板下载区域
        if (_uploadItem.templateUrl != null)
          TemplateDownloadWidget(
            fileName: '${_uploadItem.fileName}模板',
            downloadUrl: _uploadItem.templateUrl!,
            onDownload: () {
              AppSnackBar.showSuccess(context, '开始下载${_uploadItem.fileName}模板');
            },
          ),

        // 文件描述
        if (_uploadItem.description != null)
          Container(
            margin: EdgeInsets.only(left: 25.w,right: 25.w, top: 30.h),
            child: Column(
              children: [
                Text(
                  '文件描述：${_uploadItem.description!}',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: AppTheme.black999,
                    fontWeight: FontWeight.w500,
                  ),
                ),

              ],
            ),
          ),

        // 文件上传区域
        BlocBuilder<FileUploadBloc, FileUploadState>(
          builder: (context, state) {
            File? selectedFile;
            bool isUploading = false;
            double uploadProgress = 0.0;
            String? stepDescription;

            if (state is FileSelectedState) {
              selectedFile = File(state.filePath);
            } else if (state is FileUploadingState) {
              isUploading = true;
              uploadProgress = state.progress;
              stepDescription = state.stepDescription;
            }

            return Column(
              children: [
                FileUploadWidget(
                  selectedFile: selectedFile,
                  isUploading: isUploading,
                  uploadProgress: uploadProgress,
                  stepDescription: stepDescription,
                  onFileSelected: (filePath, fileName, fileSize) {
                    setState(() {
                      _selectedFile = File(filePath);
                    });

                    // 触发选择文件事件
                    context.read<FileUploadBloc>().add(
                      SelectFileEvent(
                        filePath: filePath,
                        fileName: fileName,
                        fileSize: fileSize,
                      ),
                    );
                  },
                ),

                // 上传按钮
                if (state is FileSelectedState && !isUploading) ...[
                  SizedBox(height: 20.h),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 25.w),
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // 触发开始上传事件
                        context.read<FileUploadBloc>().add(
                          StartUploadFileEvent(
                            planId: int.parse(_uploadItem.planId),
                            filePath: state.filePath,
                            fileName: state.fileName,
                            fileType: _uploadItem.fileType,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        '开始上传',
                        style: TextStyle(
                          fontSize: 32.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],

                // 重新选择按钮
                if (state is FileUploadSuccessState || state is FileUploadFailureState) ...[
                  SizedBox(height: 20.h),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 25.w),
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // 重置上传状态
                        context.read<FileUploadBloc>().add(
                          const ResetUploadStateEvent(),
                        );
                        setState(() {
                          _selectedFile = null;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        '重新选择文件',
                        style: TextStyle(
                          fontSize: 32.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ],
    );
  }

  /// 构建已上传状态的内容
  Widget _buildUploadedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 已上传文件展示
        UploadedFilesWidget(
          files: _uploadItem.uploadedFiles,
          status: _uploadItem.status,
        ),

        // 审批状态
        if (_uploadItem.approvalInfo != null)
          InfoSectionCard(
            title: '审批信息',
            icon: Image.asset('assets/images/internship_information_icon.png',width: 25.w,height: 28.h),
            child: Container(
              padding: EdgeInsets.only(top: 40.h,left: 30.w,right:  30.w,bottom: 40.h),
              child: Row(
                children: [
                  UserAvatar.large(
                    imageUrl: AppConstants.avatar2,
                    userName: '冯项老师',
                  ),
                  SizedBox(width: 17.w),
                  // 审批人信息
                  Text(
                    '冯项老师 (班主任)',
                    style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Spacer(),
                  if (_uploadItem.approvalInfo?.status == FileUploadStatus.approved)
                    _buildApprovalStatusTag("审核通过")
                  else if (_uploadItem.approvalInfo?.status == FileUploadStatus.rejected)
                    _buildApprovalStatusTag("已驳回")
                  else
                    _buildApprovalStatusTag("待审批"),
                ],
              ),
            ),
          )
      ],
    );
  }

  Widget _buildApprovalStatusTag(String status) {
    Color textColor;
    IconData? icon;

    switch (status) {
      case '待审批':
        textColor = AppTheme.primaryColor;
        icon = null;
        break;
      case '审核通过':
        textColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case '已驳回':
        textColor = Colors.red;
        icon = Icons.cancel;
        break;
      default:
        textColor = Colors.grey;
        icon = null;
    }

    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 15, color: textColor),
            const SizedBox(width: 4),
          ],
          Text(
            status,
            style: TextStyle(
              color: textColor,
              fontSize: 28.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
