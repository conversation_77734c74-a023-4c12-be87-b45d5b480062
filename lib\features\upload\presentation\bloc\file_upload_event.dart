/// -----
/// file_upload_event.dart
/// 
/// 文件上传BLoC事件定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 文件上传事件基类
abstract class FileUploadEvent extends Equatable {
  const FileUploadEvent();

  @override
  List<Object?> get props => [];
}

/// 加载文件要求列表事件
class LoadFileRequirementsEvent extends FileUploadEvent {
  /// 计划ID
  final String planId;

  const LoadFileRequirementsEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 刷新文件要求列表事件
class RefreshFileRequirementsEvent extends FileUploadEvent {
  /// 计划ID
  final String planId;

  const RefreshFileRequirementsEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 选择文件事件
class SelectFileEvent extends FileUploadEvent {
  /// 文件路径
  final String filePath;
  /// 文件名
  final String fileName;
  /// 文件大小
  final int fileSize;

  const SelectFileEvent({
    required this.filePath,
    required this.fileName,
    required this.fileSize,
  });

  @override
  List<Object?> get props => [filePath, fileName, fileSize];
}

/// 开始上传文件事件
class StartUploadFileEvent extends FileUploadEvent {
  /// 计划ID
  final int planId;
  /// 文件路径
  final String filePath;
  /// 文件名
  final String fileName;
  /// 文件类型
  final String fileType;

  const StartUploadFileEvent({
    required this.planId,
    required this.filePath,
    required this.fileName,
    required this.fileType,
  });

  @override
  List<Object?> get props => [planId, filePath, fileName, fileType];
}

/// 移除选中文件事件
class RemoveSelectedFileEvent extends FileUploadEvent {
  const RemoveSelectedFileEvent();
}

/// 重置上传状态事件
class ResetUploadStateEvent extends FileUploadEvent {
  const ResetUploadStateEvent();
}
