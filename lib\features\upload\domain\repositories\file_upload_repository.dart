/// -----
/// file_upload_repository.dart
/// 
/// 文件上传仓库接口，定义文件上传相关的数据操作
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/file_requirement.dart';
import '../usecases/upload_file_usecase.dart';

/// 文件上传仓库接口
///
/// 定义文件上传相关的操作
abstract class FileUploadRepository {
  /// 获取文件要求列表
  ///
  /// [planId] 计划ID
  /// 返回 Either<Failure, List<FileRequirement>>，表示获取文件要求列表成功或失败
  Future<Either<Failure, List<FileRequirement>>> getFileRequirements({
    required String planId,
  });

  /// 上传文件（两步流程：先上传文件，再保存信息）
  ///
  /// [filePath] 文件路径
  /// [fileName] 文件名
  /// [fileType] 文件类型
  /// [planId] 计划ID
  /// [onProgress] 上传进度回调
  /// 返回 Either<Failure, UploadFileResult>，表示上传成功或失败
  Future<Either<Failure, UploadFileResult>> uploadFile({
    required String filePath,
    required String fileName,
    required String fileType,
    required int planId,
    Function(double progress, String stepDescription)? onProgress,
  });
}
