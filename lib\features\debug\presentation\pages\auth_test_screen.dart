/// -----
/// auth_test_screen.dart
/// 
/// 认证测试页面，用于测试401错误处理逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/storage/local_storage.dart';
import '../../../../core/constants/constants.dart';

/// 认证测试页面
class AuthTestScreen extends StatefulWidget {
  const AuthTestScreen({Key? key}) : super(key: key);

  @override
  State<AuthTestScreen> createState() => _AuthTestScreenState();
}

class _AuthTestScreenState extends State<AuthTestScreen> {
  bool _isLoading = false;
  String _lastResult = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: const CustomAppBar(title: '认证拦截器测试'),
      body: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 说明文本
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                '此页面用于测试认证拦截器是否正常工作。\n'
                '点击下面的按钮会发送会返回401错误的请求，\n'
                '如果拦截器正常工作，应该会自动跳转到登录页面。',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: Colors.blue.shade700,
                ),
              ),
            ),

            SizedBox(height: 32.h),

            // 测试HTTP 401错误
            ElevatedButton(
              onPressed: _isLoading ? null : _testHttp401Error,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: _isLoading
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      '测试HTTP 401错误',
                      style: TextStyle(fontSize: 32.sp),
                    ),
            ),

            SizedBox(height: 24.h),

            // 测试拦截器顺序
            ElevatedButton(
              onPressed: _isLoading ? null : _testInterceptorOrder,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                '测试拦截器顺序',
                style: TextStyle(fontSize: 32.sp),
              ),
            ),

            SizedBox(height: 24.h),

            // 测试业务状态码401错误
            ElevatedButton(
              onPressed: _isLoading ? null : _testBusinessCode401,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: _isLoading
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      '测试业务状态码401',
                      style: TextStyle(fontSize: 32.sp),
                    ),
            ),

            SizedBox(height: 24.h),

            // 清除Token测试
            ElevatedButton(
              onPressed: _isLoading ? null : _testWithoutToken,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                '清除Token后测试',
                style: TextStyle(fontSize: 32.sp),
              ),
            ),

            SizedBox(height: 32.h),

            // 预期行为说明
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '预期行为：',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text('1. 显示警告提示："登录已过期，请重新登录"', style: TextStyle(fontSize: 26.sp)),
                  Text('2. 1.5秒后自动跳转到登录页面', style: TextStyle(fontSize: 26.sp)),
                  Text('3. 清除本地认证数据', style: TextStyle(fontSize: 26.sp)),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 结果显示
            if (_lastResult.isNotEmpty)
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '最后一次测试结果:',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      _lastResult,
                      style: TextStyle(fontSize: 26.sp),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 测试HTTP 401错误
  ///
  /// 通过访问一个不存在的端点来触发HTTP 401错误
  Future<void> _testHttp401Error() async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      final dioClient = GetIt.instance<DioClient>();

      // 访问一个会返回401的端点
      await dioClient.get('/test/unauthorized');

      setState(() {
        _lastResult = '意外：请求成功了，这不应该发生';
      });
    } on Exception catch (e) {
      setState(() {
        _lastResult = '捕获到异常: $e\n'
                     '如果拦截器正常工作，应该已经跳转到登录页面';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试业务状态码401错误
  ///
  /// 使用真实的API端点，可能会返回业务状态码10401
  Future<void> _testBusinessCode401() async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      final dioClient = GetIt.instance<DioClient>();

      // 使用真实的API端点，如果token无效会返回业务状态码10401
      final result = await dioClient.get(
        'internshipservice/v1/internship/student/file/require/list',
        queryParameters: {'planId': '8'},
      );

      setState(() {
        _lastResult = '请求成功，没有触发401错误\n响应数据: $result';
      });
    } on Exception catch (e) {
      setState(() {
        _lastResult = '捕获到异常: $e\n'
                     '如果是认证相关错误，应该已经跳转到登录页面';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清除Token后测试
  ///
  /// 先清除本地token，然后发送需要认证的请求
  Future<void> _testWithoutToken() async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      // 清除本地token
      final localStorage = GetIt.instance<LocalStorage>();
      await localStorage.remove(AppConstants.tokenKey);

      final dioClient = GetIt.instance<DioClient>();

      // 发送需要认证的请求
      await dioClient.get(
        'internshipservice/v1/internship/student/file/require/list',
        queryParameters: {'planId': '8'},
      );

      setState(() {
        _lastResult = '请求成功，但应该返回401错误';
      });
    } on Exception catch (e) {
      setState(() {
        _lastResult = '捕获到异常: $e\n'
                     '这是预期的，因为没有token';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试拦截器顺序
  ///
  /// 打印当前拦截器的执行顺序，帮助调试
  Future<void> _testInterceptorOrder() async {
    setState(() {
      _isLoading = true;
      _lastResult = '';
    });

    try {
      final dioClient = GetIt.instance<DioClient>();

      // 打印拦截器信息
      print('=== 拦截器顺序测试 ===');
      print('当前拦截器列表:');
      for (int i = 0; i < dioClient.dioInstance.interceptors.length; i++) {
        final interceptor = dioClient.dioInstance.interceptors[i];
        print('  [$i] ${interceptor.runtimeType}');
      }

      setState(() {
        _lastResult = '拦截器顺序已打印到控制台\n'
                     '请查看调试输出';
      });
    } catch (e) {
      setState(() {
        _lastResult = '测试失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
